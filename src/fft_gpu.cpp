#include "fft_gpu.hpp"
#include <stdexcept>
#include <immintrin.h>
#include <cuda_runtime.h>
#include <cstring>

FFTGPUOptimizer::FFTGPUOptimizer(int rows, int cols)
    : ROWS(rows), COLS(cols) {
    int rank = 1;
    int n[1] = { ROWS };
    int istride = COLS;
    int ostride = COLS;
    int idist = 1;
    int odist = 1;
    int inembed[1] = { ROWS };
    int onembed[1] = { ROWS };
    int batch = COLS;

    if (cufftPlanMany(&fft_col_plan_, rank, n,
                      inembed, istride, idist,
                      onembed, ostride, odist,
                      CUFFT_C2C, batch) != CUFFT_SUCCESS) {
        throw std::runtime_error("Failed to create cuFFT PlanMany for column FFT");
    }
}

FFTGPUOptimizer::~FFTGPUOptimizer() {
    cufftDestroy(fft_col_plan_);
}

void FFTGPUOptimizer::performColumnwiseFFT_GPU(float* d_complexData) {
    cufftComplex* d_data = reinterpret_cast<cufftComplex*>(d_complexData);
    if (cufftExecC2C(fft_col_plan_, d_data, d_data, CUFFT_FORWARD) != CUFFT_SUCCESS) {
        throw std::runtime_error("cuFFT column-wise execution failed");
    }
}

float* FFTGPUOptimizer::allocDeviceBuffer(size_t elements) {
    float* d_ptr = nullptr;
    if (cudaMalloc(&d_ptr, elements * sizeof(float)) != cudaSuccess) {
        throw std::runtime_error("CUDA malloc failed");
    }
    return d_ptr;
}

void FFTGPUOptimizer::freeDeviceBuffer(float* d_ptr) {
    cudaFree(d_ptr);
}
