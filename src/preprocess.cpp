#include "preprocess.hpp"
#include <thread>
#include <algorithm>

void sample_data(const std::vector<double>& input, std::vector<double>& output, size_t sample_size) {
    const size_t stride = sample_size * 2;
    const size_t n = input.size() / stride * sample_size;
    output.resize(n);
    const double* input_data = input.data();
    double* output_data = output.data();

    for (size_t i = 0; i < n; ++i) {
        output_data[i] = input_data[(i / sample_size) * stride + (i % sample_size)];
    }
}

void normalize_chunk(
    const std::vector<double>& input_tensor,
    std::vector<float>& output_tensor,
    size_t start,
    size_t end,
    double real_offset,
    double real_scale_inv,
    double imag_offset,
    double imag_scale_inv
) {
    for (size_t i = start; i < end; i += 2) {
        output_tensor[i]     = static_cast<float>((input_tensor[i] - real_offset) * real_scale_inv);
        output_tensor[i + 1] = static_cast<float>((input_tensor[i + 1] - imag_offset) * imag_scale_inv);
    }
}

void normalize_input_tensor_multithreaded(
    const std::vector<double>& input_tensor,
    std::vector<float>& output_tensor,
    size_t num_threads
) {
    const double real_offset = 842827867;
    const double real_scale_inv = 1.0 / 1707641336123;
    const double imag_offset = -214253964;
    const double imag_scale_inv = 1.0 / 1699396044280;

    const size_t num_elements = input_tensor.size();
    output_tensor.resize(num_elements);
    const size_t chunk_size = (num_elements + num_threads - 1) / num_threads;

    std::vector<std::thread> threads;
    for (size_t i = 0; i < num_threads; ++i) {
        size_t start = i * chunk_size;
        size_t end = std::min(start + chunk_size, num_elements);
        if (start % 2 != 0) --start;

        threads.emplace_back(normalize_chunk,
            std::ref(input_tensor), std::ref(output_tensor),
            start, end, real_offset, real_scale_inv, imag_offset, imag_scale_inv
        );
    }

    for (auto& thread : threads) thread.join();
}

void sample_and_normalize(const float* in,
                          size_t total_floats,
                          std::vector<float>& out)
{
    constexpr size_t kStride = 4;          // 每 4 个 float 取前 2 个
    const size_t kBlocks = total_floats / kStride;
    out.resize(kBlocks * 2);

    constexpr float real_off = 842827867.0f;
    constexpr float real_scl = 1.0f / 1707641336123.0f;
    constexpr float imag_off = -214253964.0f;
    constexpr float imag_scl = 1.0f / 1699396044280.0f;

    const size_t hw_threads = std::thread::hardware_concurrency();
    const size_t num_threads = (hw_threads == 0) ? 4 : hw_threads;
    const size_t blocks_per_thread = (kBlocks + num_threads - 1) / num_threads;

    auto worker = [&](size_t start_blk, size_t end_blk) {
        for (size_t blk = start_blk; blk < end_blk; ++blk) {
            const size_t in_idx  = blk * kStride;
            const size_t out_idx = blk * 2;

            const float real = (in[in_idx]     - real_off) * real_scl;
            const float imag = (in[in_idx + 1] - imag_off) * imag_scl;

            out[out_idx]     = real;
            out[out_idx + 1] = imag;
        }
    };

    std::vector<std::thread> threads;
    threads.reserve(num_threads);

    for (size_t t = 0; t < num_threads; ++t) {
        size_t start = t * blocks_per_thread;
        size_t end   = std::min(start + blocks_per_thread, kBlocks);
        if (start >= end) break;
        threads.emplace_back(worker, start, end);
    }

    for (auto& th : threads)
        if (th.joinable()) th.join();
}
