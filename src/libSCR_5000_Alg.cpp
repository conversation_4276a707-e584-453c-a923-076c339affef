#include "libSCR_5000_Alg.hpp"
#include "preprocess.hpp"
#include "postprocess.hpp"
#include "infer_engine.hpp"
#include "PointTracker.hpp"
#include "logger.hpp"
#include "config.hpp"
#include "fft_gpu.hpp"

#include <cuda_runtime_api.h>
#include <opencv2/opencv.hpp>
#include <chrono>
#include <iostream>
#include <fstream>
#include <memory>
#include <ctime>

// ==================== 全局资源管理 ====================
// TensorRT资源
static nvinfer1::IRuntime* g_runtime = nullptr;
static nvinfer1::ICudaEngine* g_engine = nullptr;
static nvinfer1::IExecutionContext* g_context = nullptr;
static cudaStream_t g_stream = nullptr;
static void* g_buffers[2] = {nullptr, nullptr};
static std::vector<float> g_input_tensor;
static std::vector<float> g_output_prob;
static std::vector<float> g_sample_input;
static bool g_initialized = false;

// 跟踪器资源
static std::unique_ptr<PointTracker> g_tracker = nullptr;
static std::vector<Point> g_current_group_detections;
static int g_group_start_frame = -1;
static const int FRAMES_PER_GROUP = 120;
static float g_prev_azimuth = -999.0f;
static bool g_azimuth_unchanged = false;

static std::unique_ptr<FFTGPUOptimizer> g_fft = nullptr;

// ==================== 内部辅助函数 ====================

ALGORITHM_API void GetVersionInfo(AlgorithmVersion* version_info) {
    static const AlgorithmVersion version = {
        1, 0, 0,
        "1.0.0",
        __DATE__ " " __TIME__
    };
    if (version_info) {
        *version_info = version;
    }
}

// 清理TensorRT资源的内部函数
static void CleanupTensorRTResources() {
    if (g_buffers[0]) {
        cudaFree(g_buffers[0]);
        g_buffers[0] = nullptr;
    }
    if (g_buffers[1]) {
        cudaFree(g_buffers[1]);
        g_buffers[1] = nullptr;
    }
    if (g_stream) {
        cudaStreamDestroy(g_stream);
        g_stream = nullptr;
    }
    if (g_context) {
        g_context->destroy();
        g_context = nullptr;
    }
    if (g_engine) {
        g_engine->destroy();
        g_engine = nullptr;
    }
    if (g_runtime) {
        g_runtime->destroy();
        g_runtime = nullptr;
    }
}

// 初始化TensorRT引擎
static int InitializeTensorRT(const std::string& engine_path) {

    if (g_initialized) {
        spdlog::debug("TensorRT already initialized");
        return 0;
    }

    try {
        spdlog::info("初始化TensorRT引擎: {}", engine_path);

        // 初始化自定义插件
        initializeCustomPlugins();

        // 加载引擎
        g_engine = loadEngine(engine_path, g_runtime);
        if (!g_engine) {
            spdlog::error("Failed to load TensorRT engine from: {}", engine_path);
            return -1;
        }

        g_context = g_engine->createExecutionContext();
        if (!g_context) {
            spdlog::error("Failed to create execution context");
            CleanupTensorRTResources();
            return -1;
        }

        // 创建CUDA流
        cudaError_t cuda_status = cudaStreamCreate(&g_stream);
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to create CUDA stream: {}", cudaGetErrorString(cuda_status));
            CleanupTensorRTResources();
            return -1;
        }

        // 获取输入输出维度
        const auto input_dims = g_engine->getBindingDimensions(0);
        const auto output_dims = g_engine->getBindingDimensions(1);

        const int input_h = input_dims.d[2];
        const int input_w = input_dims.d[3];
        const size_t input_size = input_dims.d[4] * input_h * input_w;
        const size_t output_size = output_dims.d[2] * output_dims.d[3];

        spdlog::info("模型输入维度: [{}x{}x{}x{}]", input_dims.d[1], input_dims.d[2], input_dims.d[3],input_dims.d[4]);

        // 分配GPU内存
        cuda_status = cudaMalloc(&g_buffers[0], input_size * sizeof(float));
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to allocate input buffer: {}", cudaGetErrorString(cuda_status));
            CleanupTensorRTResources();
            return -1;
        }

        cuda_status = cudaMalloc(&g_buffers[1], output_size * sizeof(float));
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to allocate output buffer: {}", cudaGetErrorString(cuda_status));
            CleanupTensorRTResources();
            return -1;
        }

        // 分配CPU内存
        try {
            g_input_tensor.resize(input_size);
            g_output_prob.resize(output_size);
            g_sample_input.resize(1024 * 1024 * 2);
        } catch (const std::bad_alloc& e) {
            spdlog::error("Failed to allocate CPU memory: {}", e.what());
            CleanupTensorRTResources();
            return -1;
        }

        g_initialized = true;
        spdlog::info("TensorRT引擎初始化成功");
        return 0;

    } catch (const std::exception& e) {
        spdlog::error("Exception during TensorRT initialization: {}", e.what());
        return -1;
    }
}


// 内部函数：将3帧原始雷达数据转换为结构化输入
static inline bool ExtractFrameToStructured(const void* merged, size_t frame_idx, Frame& out) noexcept{
    constexpr size_t kPulse      = 1024;
    constexpr size_t kHdr        = sizeof(FrameHeader);
    constexpr size_t kDataBytes  = kPulse * 2048 * 2 * sizeof(float);
    constexpr size_t kFrameBytes = (kHdr + kDataBytes) * 2;   // S + D

    const std::byte* base = static_cast<const std::byte*>(merged)
                          + frame_idx * kFrameBytes;

    out.S_head = reinterpret_cast<const FrameHeader*>(base);
    out.S_data = reinterpret_cast<const float*>(base + kHdr * kPulse);

    out.D_head = reinterpret_cast<const FrameHeader*>(
                    base + (kHdr + kDataBytes) * kPulse);
    out.D_data = reinterpret_cast<const float*>(
                    base + (kHdr + kDataBytes) * kPulse + kHdr * kPulse);
    return true;
}


// ==================== 公共API函数 ====================

// 算法库初始化

ALGORITHM_API int InitializeAlgorithmLibrary(const char* config_path) {
    if (!config_path) {
        spdlog::error("Invalid config path");
        return -1;
    }
    // 加载配置
    ConfigManager config_manager(config_path);

    // 日志路径
    std::string log_path = *config_manager.get<std::string>("/io_settings/logging/log_file_path");

    // 初始化日志
    initLogger(log_path);

    // 初始化TensorRT引擎（如果尚未初始化）
    if (!g_initialized) {
        
        std::string engine_path = *config_manager.get<std::string>("/io_settings/engine_paths");

        int init_result = -1;
        std::ifstream test_file(engine_path);
            if (test_file.good()) {
            init_result = InitializeTensorRT(engine_path);
        }

        if (init_result != 0) {
            spdlog::error("Failed to initialize TensorRT engine");
            return -2;
        }
    }

    // 加载查表数据
    std::string table_path = *config_manager.get<std::string>("/io_settings/table_paths");

    bool table_loaded = false;
    std::ifstream test_file(table_path);
        if (test_file.good()) {
        spdlog::info("加载俯仰角查表数据: {}", table_path);
        loadHechaTable_(table_path);
            table_loaded = true;
    }

    if (!table_loaded) {
        spdlog::warn("Could not find hecha_table.csv, using default values");
    }

    // 初始化跟踪器(json配置文件修改)
    g_tracker = std::make_unique<PointTracker>(
        *config_manager.get<int>("/algorithm_settings/tracker/max_age"),
        *config_manager.get<int>("/algorithm_settings/tracker/reid_age"),
        *config_manager.get<float>("/algorithm_settings/tracker/distance_threshold"),
        *config_manager.get<int>("/algorithm_settings/tracker/min_hits")
    );
    spdlog::info("跟踪器初始化成功: max_age={}, reid_age={}, distance_threshold={}, min_hits={}",
        *config_manager.get<int>("/algorithm_settings/tracker/max_age"),
        *config_manager.get<int>("/algorithm_settings/tracker/reid_age"),
        *config_manager.get<float>("/algorithm_settings/tracker/distance_threshold"),
        *config_manager.get<int>("/algorithm_settings/tracker/min_hits")
    );

    // 初始化GPU FFT优化器
    g_fft = std::make_unique<FFTGPUOptimizer>(1024, 2048);

    return 0;
}

// 目标检测算法实现 - 结构化数据输入
ALGORITHM_API int TargetDetection(
    char* input_data,
    DetectionResult** detection_results,
    int* num_detections
) {
    // 目标检测算法开始
    spdlog::info("=====开始执行目标检测...=====");

    // 参数验证
    if (!input_data || !detection_results || !num_detections) {
        spdlog::error("Invalid input parameters for TargetDetection");
        return -1;
    }

    // 输入数据结构化
    Frame radar_data;
    
    // 统计耗时
    int64_t start_time = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
    if (!ExtractFrameToStructured(input_data,0,radar_data)){
        spdlog::error("Failed to parse input data");
        return -2;
    }
    int64_t end_time = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
    spdlog::info("结构化数据耗时: {} ms", end_time - start_time);

    // 初始化输出参数
    *detection_results = nullptr;
    *num_detections = 0;


    try {

        // 预处理：采样和归一化
        auto t0 = std::chrono::high_resolution_clock::now();
        // TODO:输入数据尺寸能不能优化
        sample_and_normalize(radar_data.S_data, 1024 * 2048 * 2, g_sample_input);
        auto t1 = std::chrono::high_resolution_clock::now();

        // 上传数据到GPU
        const int inputIndex = 0, outputIndex = 1;
        cudaError_t cuda_status = cudaMemcpyAsync(
            g_buffers[inputIndex],
            g_sample_input.data(),
            g_sample_input.size() * sizeof(float),
            cudaMemcpyHostToDevice,
            g_stream
        );
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to copy input data to GPU: {}", cudaGetErrorString(cuda_status));
            return -1;
        }
        cudaStreamSynchronize(g_stream);
        auto t2 = std::chrono::high_resolution_clock::now();

        // 执行推理
        bool inference_result = g_context->enqueueV2(g_buffers, g_stream, nullptr);
        if (!inference_result) {
            spdlog::error("TensorRT inference failed");
            return -1;
        }
        cudaStreamSynchronize(g_stream);
        auto t3 = std::chrono::high_resolution_clock::now();

        // 下载结果
        cuda_status = cudaMemcpyAsync(
            g_output_prob.data(),
            g_buffers[outputIndex],
                       g_output_prob.size() * sizeof(float),
            cudaMemcpyDeviceToHost,
            g_stream
        );
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to copy output data from GPU: {}", cudaGetErrorString(cuda_status));
            return -1;
        }
        cudaStreamSynchronize(g_stream);
        auto t4 = std::chrono::high_resolution_clock::now();

        // 后处理：获取检测中心点
        auto centers = post_process(g_output_prob.data(), 512, 1024);
        auto t5 = std::chrono::high_resolution_clock::now();

        // 分配GPU内存并上传数据
        float* d_S_complexData = g_fft->allocDeviceBuffer(4194304);
        float* d_D_complexData = g_fft->allocDeviceBuffer(4194304);
        cudaMemcpyAsync(d_S_complexData, radar_data.S_data, 4194304 * sizeof(float), cudaMemcpyHostToDevice, g_stream);
        cudaMemcpyAsync(d_D_complexData, radar_data.D_data, 4194304 * sizeof(float), cudaMemcpyHostToDevice, g_stream);

        // 在GPU上执行列FFT
        g_fft->performColumnwiseFFT_GPU(d_S_complexData);
        g_fft->performColumnwiseFFT_GPU(d_D_complexData);

        // 下载复数数据
        std::vector<std::complex<float>> S_complexData(4194304 / 2);
        std::vector<std::complex<float>> D_complexData(4194304 / 2);
        cudaMemcpyAsync(S_complexData.data(), d_S_complexData, S_complexData.size() * sizeof(std::complex<float>), cudaMemcpyDeviceToHost, g_stream);
        cudaMemcpyAsync(D_complexData.data(), d_D_complexData, D_complexData.size() * sizeof(std::complex<float>), cudaMemcpyDeviceToHost, g_stream);

        // 释放GPU内存
        g_fft->freeDeviceBuffer(d_S_complexData);
        g_fft->freeDeviceBuffer(d_D_complexData);

        auto t6 = std::chrono::high_resolution_clock::now();

        // 计算俯仰角、方位角、距离等信息
        auto results = computeElevationAngles_GPU(radar_data.S_head, S_complexData, D_complexData, centers, 1024, 2048);
        auto t7 = std::chrono::high_resolution_clock::now();

        if (results.empty()) {
            spdlog::warn("模型没有检测到目标");
            *detection_results = nullptr;
            *num_detections = 0;
            return 0;
        }

        // 分配输出内存
            *num_detections = results.size();
        *detection_results = new DetectionResult[*num_detections];

        spdlog::info("检测到 {} 个目标", *num_detections);

        // 填充检测结果
        // 统计耗时
        int64_t start_time_1 = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
            for (size_t i = 0; i < results.size(); ++i) {
            const auto& [vx, vy, vz, x, y, z, fMV, fMR, fMA, fME, frame, row, col] = results[i];

            (*detection_results)[i].x = x;
            (*detection_results)[i].y = y;
            (*detection_results)[i].z = z;
            (*detection_results)[i].vx = vx;
            (*detection_results)[i].vy = vy;
            (*detection_results)[i].vz = vz;
            (*detection_results)[i].fMV = fMV;
            (*detection_results)[i].fMR = fMR;
            (*detection_results)[i].fMA = fMA;
            (*detection_results)[i].fME = fME;
            (*detection_results)[i].frame = frame;
            (*detection_results)[i].row = row;
            (*detection_results)[i].col = col;
            (*detection_results)[i].type = 1; // 默认类型
        }
        int64_t end_time_1 = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
        spdlog::info("填充检测结果耗时: {} ms", end_time_1 - start_time_1);

        spdlog::info("{:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10}",
                     "Vx(m/s)", "Vy(m/s)", "Vz(m/s)", "X(m)", "Y(m)", "Z(m)", "Velo", "Range", "Amaz", "Elev", "Frame", "X_cor", "Y_cor");

        for (const auto& [vx, vy, vz, x, y, z, fMV, fMR, fMA, fME, frame, row, col] : results) {
            spdlog::info("{:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10} {:<10} {:<10}",
                        vx, vy, vz, x, y, z, fMV, fMR, fMA, fME, frame, row, col);
        }

        // 记录性能信息
        int t_pre = std::chrono::duration_cast<std::chrono::milliseconds>(t1 - t0).count();
        int t_gpuin = std::chrono::duration_cast<std::chrono::milliseconds>(t2 - t1).count();
        int t_inf = std::chrono::duration_cast<std::chrono::milliseconds>(t3 - t2).count();
        int t_gpuout = std::chrono::duration_cast<std::chrono::milliseconds>(t4 - t3).count();
        int t_post = std::chrono::duration_cast<std::chrono::milliseconds>(t5 - t4).count();
        int t_compute_1 = std::chrono::duration_cast<std::chrono::milliseconds>(t6 - t5).count();
        int t_compute_2 = std::chrono::duration_cast<std::chrono::milliseconds>(t7 - t6).count();
        int total = t_pre + t_gpuin + t_gpuout + t_post + t_inf + t_compute_1 + t_compute_2;

        spdlog::info("目标检测耗时(ms): 预处理:{} 上传:{} 推理:{} 下载:{} 后处理:{} 计算1:{} 计算2:{} 总:{} (FPS:{:.2f})",
                     t_pre, t_gpuin, t_inf, t_gpuout, t_post, t_compute_1, t_compute_2 ,total, 1000.0 / total);

        return 0;

    } catch (const std::exception& e) {
        spdlog::error("Exception in TargetDetection: {}", e.what());
        return -1;
    }
}

// 目标跟踪算法实现
ALGORITHM_API void TargetTracking(
    const DetectionResult* detection_results,
    int num_detections,
    TrackingResult** tracking_results,
    int* num_tracks
) {
    // if (!detection_results || !tracking_results || !num_tracks) {
    //     spdlog::error("Invalid input parameters for TargetTracking");
    //     return;
    // }

    // 开始目标跟踪
    spdlog::info("=====开始目标跟踪...======");

    if (num_detections <= 0) {
        spdlog::warn("没有检测到目标，跳过跟踪");
        *tracking_results = nullptr;
        *num_tracks = 0;
        return;
    }

    // 确保跟踪器已初始化
    if (!g_tracker) {
        g_tracker = std::make_unique<PointTracker>(3, 20, 50.0f, 2);
        spdlog::info("跟踪器使用默认参数：max_age=3, reid_age=20, distance_threshold=50.0f, min_hits=2");
    }

    try {
        // 将检测结果转换为Point格式
        std::vector<Point> current_detections;
        current_detections.reserve(num_detections);

        for (int i = 0; i < num_detections; ++i) {
            Point p;
            p.position[0] = detection_results[i].x;
            p.position[1] = detection_results[i].y;
            p.position[2] = detection_results[i].z;
            p.velocity[0] = detection_results[i].vx;
            p.velocity[1] = detection_results[i].vy;
            p.velocity[2] = detection_results[i].vz;
            p.type = detection_results[i].type;
            p.frame = detection_results[i].frame;
            p.label = -1; // 初始化标签
            current_detections.push_back(p);
        }

        // 检查是否需要处理分组逻辑
        int current_frame = detection_results[0].frame;
        float current_azimuth = detection_results[0].fMA;

        // 检查方位角是否变化
        g_azimuth_unchanged = (g_prev_azimuth != -999.0f) &&
                              (std::abs(current_azimuth - g_prev_azimuth) < 1.0f);
        g_prev_azimuth = current_azimuth;

        // 添加到当前组
        for (const auto& detection : current_detections) {
            g_current_group_detections.push_back(detection);
        }

        if (g_group_start_frame == -1) {
            g_group_start_frame = current_frame;
        }

        // 判断是否需要处理当前组
        bool should_process_group = false;
        if (g_azimuth_unchanged) {
            should_process_group = true;
            spdlog::info("云台未转动");
        } else {
            int frame_diff = current_frame - g_group_start_frame;
            if (frame_diff < 0) frame_diff += 65536; // 处理帧号回绕
            if (frame_diff >= FRAMES_PER_GROUP - 1) {
                should_process_group = true;
            }
        }

        std::vector<TrackResult> tracks;

        if (should_process_group && !g_current_group_detections.empty()) {
            spdlog::info("目标数量:{}, 起始帧:{}, 结束帧:{}",
                        g_current_group_detections.size(), g_group_start_frame, current_frame);

            // 聚类检测结果
            // auto clustered = clusterDetections(g_current_group_detections, 10.0f);
            // spdlog::info("聚类后数量: {}", clustered.size());

            // 执行跟踪
            tracks = g_tracker->update(g_current_group_detections);
            // auto tracked_info = convertTracksToTargetInfos(tracks);
            spdlog::info("跟踪结果数量: {}", tracks.size());

            // 轨迹插值
            auto interpolated_tracks = g_tracker->interpolateTracks_seg(1.0f);
            for (const auto& step : interpolated_tracks) {
                // 等待1秒
                // std::this_thread::sleep_for(std::chrono::milliseconds(500));
                // auto step_info = convertTracksToTargetInfos(step);
                tracks.insert(tracks.end(), step.begin(), step.end());
            }

            // 清理当前组
            g_current_group_detections.clear();
            g_group_start_frame = g_azimuth_unchanged ? -1 : current_frame + 1;
        } else {
            // 如果不需要处理组，返回空结果
            *tracking_results = nullptr;
            *num_tracks = 0;
            return;
        }

        // 转换跟踪结果
        if (tracks.empty()) {
            *tracking_results = nullptr;
            *num_tracks = 0;
            return;
        }

        // 分配输出内存
        *num_tracks = tracks.size();
        *tracking_results = new TrackingResult[*num_tracks];

        // 填充跟踪结果
        for (size_t i = 0; i < tracks.size(); ++i) {
            const auto& track = tracks[i];

            (*tracking_results)[i].id = track.id;
            (*tracking_results)[i].x = track.position[0];
            (*tracking_results)[i].y = track.position[1];
            (*tracking_results)[i].z = track.position[2];
            (*tracking_results)[i].vx = track.velocity[0];
            (*tracking_results)[i].vy = track.velocity[1];
            (*tracking_results)[i].vz = track.velocity[2];

            // 计算转换值
            float range = std::sqrt(track.position[0]*track.position[0] +
                                  track.position[1]*track.position[1] +
                                  track.position[2]*track.position[2]);
            (*tracking_results)[i].fMR = range;
            (*tracking_results)[i].fMV = (track.position[0]*track.velocity[0] +
                                         track.position[1]*track.velocity[1] +
                                         track.position[2]*track.velocity[2]) / (range + 1e-6f);
            (*tracking_results)[i].fMA = std::fmod(std::atan2(track.position[1], track.position[0]) * 180.0f / M_PI + 360.0f, 360.0f);
            (*tracking_results)[i].fME = std::atan2(track.position[2], std::sqrt(track.position[0]*track.position[0] + track.position[1]*track.position[1])) * 180.0f / M_PI;

            // 设置默认值
            (*tracking_results)[i].fSNR = 1.0f;
            (*tracking_results)[i].fEn = 1.0f;
            (*tracking_results)[i].fRcs = 1.0f;
            (*tracking_results)[i].type = 1;
            (*tracking_results)[i].FPGATimeLog = 1;
            (*tracking_results)[i].PreShow = 2;

            spdlog::info("Track[{:>2}] ID: {:>2} | Pos: ({:>7.2f}, {:>7.2f}, {:>7.2f}) m | Vel: ({:>6.2f}, {:>6.2f}, {:>6.2f}) m/s | R: {:>6.2f} m | Vr: {:>6.2f} m/s | Az: {:>6.2f}° | El: {:>6.2f}°",
                i,
                (*tracking_results)[i].id,
                (*tracking_results)[i].x, (*tracking_results)[i].y, (*tracking_results)[i].z,
                (*tracking_results)[i].vx, (*tracking_results)[i].vy, (*tracking_results)[i].vz,
                (*tracking_results)[i].fMR, (*tracking_results)[i].fMV,
                (*tracking_results)[i].fMA, (*tracking_results)[i].fME);
        }

        // 跟踪结果输出到日志文件
        spdlog::info("跟踪完成，输出 {} 个轨迹", *num_tracks);

        return;

    } catch (const std::exception& e) {
        spdlog::error("Exception in TargetTracking: {}", e.what());
        return;
    }
}

// 释放检测结果内存
ALGORITHM_API void ReleaseDetectionResults(DetectionResult* detection_results) {
    if (detection_results) {
        delete[] detection_results;
        spdlog::debug("Detection results memory released");
    }
}

// 释放跟踪结果内存
ALGORITHM_API void ReleaseTrackingResults(TrackingResult* tracking_results) {
    if (tracking_results) {
        delete[] tracking_results;
        spdlog::debug("Tracking results memory released");
    }
}

// 释放所有资源
ALGORITHM_API void ReleaseAllResources() {
    try {
        spdlog::info("Starting resource cleanup...");

        // 释放TensorRT和CUDA资源
        CleanupTensorRTResources();

        // 清理CPU内存
        g_input_tensor.clear();
        g_input_tensor.shrink_to_fit();
        g_output_prob.clear();
        g_output_prob.shrink_to_fit();
        g_sample_input.clear();
        g_sample_input.shrink_to_fit();

        // 重置跟踪器和相关状态
        g_tracker.reset();
        g_current_group_detections.clear();
        g_current_group_detections.shrink_to_fit();
        g_group_start_frame = -1;
        g_prev_azimuth = -999.0f;
        g_azimuth_unchanged = false;

        // 重置初始化标志
        g_initialized = false;

        spdlog::info("All resources released successfully");

    } catch (const std::exception& e) {
        spdlog::error("Exception during resource cleanup: {}", e.what());
    }
}