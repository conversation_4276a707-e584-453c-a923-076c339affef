# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Test

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Test/build

# Include any dependencies generated for this target.
include CMakeFiles/test_algorithm.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_algorithm.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_algorithm.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_algorithm.dir/flags.make

CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.o: CMakeFiles/test_algorithm.dir/flags.make
CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.o: ../test/test_algorithm.cpp
CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.o: CMakeFiles/test_algorithm.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.o -MF CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.o.d -o CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/test/test_algorithm.cpp

CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/test/test_algorithm.cpp > CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.i

CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/test/test_algorithm.cpp -o CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.s

# Object files for target test_algorithm
test_algorithm_OBJECTS = \
"CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.o"

# External object files for target test_algorithm
test_algorithm_EXTERNAL_OBJECTS =

test_algorithm: CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.o
test_algorithm: CMakeFiles/test_algorithm.dir/build.make
test_algorithm: libSCR_5000_Alg.so.1.0.0
test_algorithm: CMakeFiles/test_algorithm.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable test_algorithm"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_algorithm.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_algorithm.dir/build: test_algorithm
.PHONY : CMakeFiles/test_algorithm.dir/build

CMakeFiles/test_algorithm.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_algorithm.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_algorithm.dir/clean

CMakeFiles/test_algorithm.dir/depend:
	cd /home/<USER>/My_Project/MSHNet_TensorRT_Test/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/My_Project/MSHNet_TensorRT_Test /home/<USER>/My_Project/MSHNet_TensorRT_Test /home/<USER>/My_Project/MSHNet_TensorRT_Test/build /home/<USER>/My_Project/MSHNet_TensorRT_Test/build /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles/test_algorithm.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/test_algorithm.dir/depend

