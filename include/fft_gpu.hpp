#pragma once

#include <vector>
#include <complex>
#include <cufft.h>

class FFTGPUOptimizer {
public:
    FFTGPUOptimizer(int rows, int cols);
    ~FFTGPUOptimizer();

    // 复数FFT（GPU列方向执行）
    void performColumnwiseFFT_GPU(float* d_complexData);

    // 设备内存管理
    float* allocDeviceBuffer(size_t elements);
    void freeDeviceBuffer(float* d_ptr);

    // 维度访问
    int getRows() const { return ROWS; }
    int getCols() const { return COLS; }

private:
    int ROWS;
    int COLS;
    cufftHandle fft_col_plan_;
};
